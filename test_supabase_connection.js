// Test Supabase Connection - Run this in browser console to debug
// Copy and paste this into your browser's developer console while on your form page

console.log('=== Supabase Connection Test ===');

// Check environment variables
console.log('VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('VITE_SUPABASE_ANON_KEY (first 20 chars):', 
  import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 20) + '...');

// Import your supabase client
import { supabase } from './src/lib/supabase.ts';

if (!supabase) {
  console.error('❌ Supabase client is null - check your environment variables');
} else {
  console.log('✅ Supabase client created successfully');
  console.log('Supabase URL:', supabase.supabaseUrl);
  console.log('Supabase Key (first 20 chars):', supabase.supabaseKey.substring(0, 20) + '...');
  
  // Test a simple query to check connection and auth
  console.log('Testing connection...');
  
  supabase
    .from('leads')
    .select('count', { count: 'exact', head: true })
    .then(({ data, error, count }) => {
      if (error) {
        console.error('❌ Connection test failed:', error);
        console.error('Error code:', error.code);
        console.error('Error message:', error.message);
        console.error('Error details:', error.details);
        console.error('Error hint:', error.hint);
      } else {
        console.log('✅ Connection test successful');
        console.log('Total leads count:', count);
      }
    });
  
  // Test insert with minimal data
  console.log('Testing insert permission...');
  
  const testLead = {
    first_name: 'Test',
    last_name: 'User',
    phone: '555-0000',
    email: 'test-' + Date.now() + '@example.com',
    address: '123 Test St',
    agree_to_terms: true,
    source: 'Connection Test'
  };
  
  supabase
    .from('leads')
    .insert([testLead])
    .select()
    .single()
    .then(({ data, error }) => {
      if (error) {
        console.error('❌ Insert test failed:', error);
        console.error('This is the same error you\'re seeing in the form');
        console.error('Error code:', error.code);
        console.error('Error message:', error.message);
      } else {
        console.log('✅ Insert test successful:', data);
        
        // Clean up test record
        supabase
          .from('leads')
          .delete()
          .eq('id', data.id)
          .then(() => {
            console.log('✅ Test record cleaned up');
          });
      }
    });
}

console.log('=== End Test ===');
