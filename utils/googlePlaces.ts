// Google Places API utilities
declare global {
  interface Window {
    google: any;
    initMap: () => void;
  }
}

let isGoogleMapsLoaded = false;
let googleMapsPromise: Promise<void> | null = null;

export const loadGoogleMapsAPI = (): Promise<void> => {
  if (isGoogleMapsLoaded) {
    return Promise.resolve();
  }

  if (googleMapsPromise) {
    return googleMapsPromise;
  }

  googleMapsPromise = new Promise((resolve, reject) => {
    // Check if API key is available
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    if (!apiKey || apiKey === 'your_google_maps_api_key') {
      console.warn('Google Maps API key not configured. Address autocomplete will be disabled.');
      resolve();
      return;
    }

    // Check if Google Maps is already loaded
    if (window.google && window.google.maps) {
      isGoogleMapsLoaded = true;
      resolve();
      return;
    }

    // Create script element
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      isGoogleMapsLoaded = true;
      resolve();
    };

    script.onerror = () => {
      console.error('Failed to load Google Maps API. Please check your API key and ensure Places API is enabled.');
      resolve(); // Resolve instead of reject to allow form to work without autocomplete
    };

    document.head.appendChild(script);
  });

  return googleMapsPromise;
};

export const initializeAddressAutocomplete = (inputElement: HTMLInputElement): void => {
  // Check if Google Maps API key is configured
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
  if (!apiKey || apiKey === 'your_google_maps_api_key') {
    console.warn('Google Maps API key not configured. Address autocomplete is disabled.');
    return;
  }

  // Check if Google Maps Places API is loaded
  if (!window.google || !window.google.maps || !window.google.maps.places) {
    console.warn('Google Maps Places API not loaded. Address autocomplete is disabled.');
    return;
  }

  try {
    const autocomplete = new window.google.maps.places.Autocomplete(inputElement, {
      types: ['address'],
      componentRestrictions: { country: 'us' },
      fields: ['formatted_address', 'geometry', 'address_components']
    });

    autocomplete.addListener('place_changed', () => {
      const place = autocomplete.getPlace();
      if (place.formatted_address) {
        // Update the input value
        inputElement.value = place.formatted_address;
        
        // Trigger a change event so React state updates
        const event = new Event('input', { bubbles: true });
        inputElement.dispatchEvent(event);
      }
    });
  } catch (error) {
    console.error('Failed to initialize Google Places autocomplete:', error);
  }
};