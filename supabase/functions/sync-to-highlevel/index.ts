import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface HighLevelContact {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address1: string;
  source: string;
  customFields?: Array<{
    id: string;
    value: string;
  }>;
  tags?: string[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { leadId } = await req.json()

    if (!leadId) {
      throw new Error('Lead ID is required')
    }

    // Get lead data from Supabase
    const { data: lead, error: leadError } = await supabaseClient
      .from('leads')
      .select('*')
      .eq('id', leadId)
      .single()

    if (leadError || !lead) {
      throw new Error(`Failed to fetch lead: ${leadError?.message}`)
    }

    // Prepare HighLevel contact data
    const contactData: HighLevelContact = {
      firstName: lead.first_name,
      lastName: lead.last_name,
      phone: lead.phone,
      email: lead.email,
      address1: lead.address,
      source: lead.source,
      tags: ['Website Lead', 'Project Estimate']
    }

    // Add custom fields if image was uploaded
    if (lead.project_image_url) {
      contactData.customFields = [
        {
          id: 'project_image_url', // You'll need to create this custom field in HighLevel
          value: lead.project_image_url
        },
        {
          id: 'project_image_filename',
          value: lead.project_image_filename || 'project_image.jpg'
        }
      ]
    }

    // Create contact in HighLevel
    const highlevelResponse = await fetch(
      `https://services.leadconnectorhq.com/contacts/`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${Deno.env.get('HIGHLEVEL_API_KEY')}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        },
        body: JSON.stringify({
          ...contactData,
          locationId: Deno.env.get('HIGHLEVEL_LOCATION_ID')
        })
      }
    )

    if (!highlevelResponse.ok) {
      const errorText = await highlevelResponse.text()
      throw new Error(`HighLevel API error: ${highlevelResponse.status} - ${errorText}`)
    }

    const highlevelContact = await highlevelResponse.json()

    // Update lead with HighLevel contact ID
    const { error: updateError } = await supabaseClient
      .from('leads')
      .update({ highlevel_contact_id: highlevelContact.contact.id })
      .eq('id', leadId)

    if (updateError) {
      console.error('Failed to update lead with HighLevel contact ID:', updateError)
      // Don't throw error here as the main sync was successful
    }

    // If there's a project image, try to upload it to HighLevel
    if (lead.project_image_url) {
      try {
        // Download image from Supabase
        const imageResponse = await fetch(lead.project_image_url)
        const imageBlob = await imageResponse.blob()

        // Upload to HighLevel media library
        const formData = new FormData()
        formData.append('file', imageBlob, lead.project_image_filename || 'project_image.jpg')
        formData.append('contactId', highlevelContact.contact.id)

        const mediaUploadResponse = await fetch(
          `https://services.leadconnectorhq.com/medias/upload-file`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${Deno.env.get('HIGHLEVEL_API_KEY')}`,
              'Version': '2021-07-28'
            },
            body: formData
          }
        )

        if (mediaUploadResponse.ok) {
          console.log('Successfully uploaded image to HighLevel media library')
        } else {
          console.error('Failed to upload image to HighLevel:', await mediaUploadResponse.text())
        }
      } catch (imageError) {
        console.error('Error uploading image to HighLevel:', imageError)
        // Don't fail the entire sync for image upload issues
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        highlevelContactId: highlevelContact.contact.id 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error syncing to HighLevel:', error)
    return new Response(
      JSON.stringify({ 
        error: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})