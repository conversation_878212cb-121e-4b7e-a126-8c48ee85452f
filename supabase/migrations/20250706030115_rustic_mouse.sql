/*
  # Fix RLS policies for leads table

  This migration completely resets the RLS policies for the leads table to ensure
  anonymous users can insert leads through the form.

  ## Changes Made
  1. Drop all existing INSERT policies
  2. Create simple, working policies for both anon and authenticated users
  3. Verify the policies are correctly applied

  ## Security
  - Anonymous users can only INSERT leads (for form submissions)
  - Authenticated users can SELECT, UPDATE, and INSERT leads
  - No DELETE permissions for anonymous users
*/

-- First, let's see what policies currently exist
DO $$
BEGIN
    RAISE NOTICE 'Current policies on leads table:';
END $$;

-- Drop ALL existing INSERT policies to avoid conflicts
DROP POLICY IF EXISTS "anon_insert_leads" ON public.leads;
DROP POLICY IF EXISTS "authenticated_insert_leads" ON public.leads;
DROP POLICY IF EXISTS "Allow anonymous lead creation" ON public.leads;
DROP POLICY IF EXISTS "Allow authenticated lead creation" ON public.leads;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON public.leads;
DROP POLICY IF EXISTS "Enable insert for anon users" ON public.leads;
DROP POLICY IF EXISTS "Allow anon insert" ON public.leads;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.leads;
DROP POLICY IF EXISTS "Users can insert own leads" ON public.leads;

-- Also drop any conflicting SELECT/UPDATE policies that might interfere
DROP POLICY IF EXISTS "Allow authenticated read" ON public.leads;
DROP POLICY IF EXISTS "Allow authenticated update" ON public.leads;

-- Create fresh, simple policies
CREATE POLICY "anon_insert_leads"
  ON public.leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

CREATE POLICY "authenticated_insert_leads"
  ON public.leads
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "authenticated_select_leads"
  ON public.leads
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "authenticated_update_leads"
  ON public.leads
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Ensure RLS is enabled
ALTER TABLE public.leads ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to anon role
GRANT INSERT ON public.leads TO anon;
GRANT USAGE ON SCHEMA public TO anon;

-- Grant permissions to authenticated role
GRANT SELECT, INSERT, UPDATE ON public.leads TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Verify the setup
DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE tablename = 'leads' AND schemaname = 'public' AND cmd = 'INSERT';
    
    RAISE NOTICE 'Number of INSERT policies on leads table: %', policy_count;
    
    IF policy_count >= 2 THEN
        RAISE NOTICE 'SUCCESS: INSERT policies created successfully';
    ELSE
        RAISE WARNING 'WARNING: Expected at least 2 INSERT policies, found %', policy_count;
    END IF;
END $$;