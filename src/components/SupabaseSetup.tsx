import React from 'react';
import { AlertCircle, Database, ExternalLink } from 'lucide-react';

export const SupabaseSetup: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8">
        <div className="text-center mb-6">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Supabase Setup Required
          </h1>
          <p className="text-gray-600">
            To use this application, you need to connect to Supabase first.
          </p>
        </div>

        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Database className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-1">
                  Connect to Supabase
                </h3>
                <p className="text-sm text-blue-700 mb-3">
                  Click the "Connect to Supabase" button in the top right corner to set up your database connection.
                </p>
                <div className="text-xs text-blue-600 bg-blue-100 rounded px-2 py-1 inline-block">
                  This will automatically configure your environment variables
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-2">
              What happens next?
            </h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Database tables will be created automatically</li>
              <li>• Storage bucket for images will be set up</li>
              <li>• Edge functions will be deployed</li>
              <li>• Environment variables will be configured</li>
            </ul>
          </div>

          <div className="text-center pt-4">
            <a
              href="https://supabase.com/docs"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700 transition-colors"
            >
              <ExternalLink className="w-4 h-4 mr-1" />
              Learn more about Supabase
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};