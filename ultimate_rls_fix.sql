-- ULTIMATE RLS FIX - This will handle all edge cases
-- Run this in Supabase SQL Editor

-- Step 1: Check current user and permissions
SELECT current_user, current_setting('role') as current_role;

-- Step 2: Ensure we're working as the right user
SET ROLE postgres;

-- Step 3: Completely disable <PERSON><PERSON> and clean everything
ALTER TABLE public.leads DISABLE ROW LEVEL SECURITY;

-- Step 4: Drop ALL policies with extreme prejudice
DO $$
DECLARE
    policy_record RECORD;
    policy_count INTEGER := 0;
BEGIN
    -- Drop all policies on leads table
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'leads' AND schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE format('DROP POLICY %I ON public.leads', policy_record.policyname);
            policy_count := policy_count + 1;
            RAISE NOTICE 'Dropped policy: %', policy_record.policyname;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not drop policy %, error: %', policy_record.policyname, SQLERRM;
        END;
    <PERSON><PERSON> LOOP;
    
    RAISE NOTICE 'Dropped % policies total', policy_count;
END $$;

-- Step 5: Grant explicit permissions to anon role
GRANT INSERT ON public.leads TO anon;
GRANT SELECT ON public.leads TO authenticated;
GRANT INSERT ON public.leads TO authenticated;
GRANT UPDATE ON public.leads TO authenticated;

-- Step 6: Re-enable RLS
ALTER TABLE public.leads ENABLE ROW LEVEL SECURITY;

-- Step 7: Create ultra-simple policies
-- The most permissive INSERT policy possible for anon
CREATE POLICY "anon_insert_policy"
  ON public.leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- Simple policy for authenticated users
CREATE POLICY "auth_all_policy"
  ON public.leads
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Step 8: Verify table permissions
SELECT 
  grantee,
  privilege_type
FROM information_schema.table_privileges 
WHERE table_name = 'leads' 
  AND table_schema = 'public'
  AND grantee IN ('anon', 'authenticated', 'public');

-- Step 9: Verify policies
SELECT 
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'leads' AND schemaname = 'public';

-- Step 10: Test with explicit role setting
SET ROLE anon;
SELECT current_user, current_setting('role') as role_check;

-- Test insert as anon (this should work)
INSERT INTO public.leads (
  first_name, 
  last_name, 
  phone, 
  email, 
  address, 
  agree_to_terms, 
  source
) VALUES (
  'RLS_Test', 
  'Success', 
  '555-TEST', 
  '<EMAIL>', 
  '123 RLS Test St', 
  true, 
  'RLS Test'
);

-- Reset role
RESET ROLE;

-- Step 11: Verify the test record was inserted
SELECT id, first_name, last_name, email, created_at 
FROM public.leads 
WHERE email = '<EMAIL>';

-- Step 12: Clean up test record
DELETE FROM public.leads WHERE email = '<EMAIL>';

-- Step 13: Final verification
DO $$
DECLARE
    rls_enabled BOOLEAN;
    policy_count INTEGER;
BEGIN
    -- Check RLS status
    SELECT rowsecurity INTO rls_enabled
    FROM pg_tables 
    WHERE tablename = 'leads' AND schemaname = 'public';
    
    -- Count policies
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE tablename = 'leads' AND schemaname = 'public';
    
    RAISE NOTICE 'RLS enabled: %, Policy count: %', rls_enabled, policy_count;
    
    IF rls_enabled AND policy_count >= 1 THEN
        RAISE NOTICE 'SUCCESS: RLS is properly configured for anonymous form submissions!';
    ELSE
        RAISE EXCEPTION 'FAILED: RLS configuration is not correct';
    END IF;
END $$;
