import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Check if Supabase is properly configured
const isSupabaseConfigured = () => {
  return supabaseUrl && 
         supabaseAnonKey && 
         supabaseUrl !== 'your_supabase_project_url' && 
         supabaseAnonKey !== 'your_supabase_anon_key' &&
         supabaseUrl.startsWith('https://');
};

// Only create client if properly configured
export const supabase = isSupabaseConfigured() 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null;

export interface Lead {
  id?: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  address: string;
  project_image_url?: string;
  project_image_filename?: string;
  agree_to_terms: boolean;
  source: string;
  highlevel_contact_id?: string;
  created_at?: string;
}

const ensureSupabaseClient = () => {
  if (!supabase) {
    throw new Error('Supabase is not configured. Please click "Connect to Supabase" in the top right to set up your project.');
  }
  return supabase;
};

export const uploadImage = async (file: File): Promise<string> => {
  const client = ensureSupabaseClient();
  
  const fileExt = file.name.split('.').pop();
  const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
  const filePath = `project-images/${fileName}`;

  const { error: uploadError } = await client.storage
    .from('project-images')
    .upload(filePath, file);

  if (uploadError) {
    throw new Error(`Failed to upload image: ${uploadError.message}`);
  }

  const { data } = client.storage
    .from('project-images')
    .getPublicUrl(filePath);

  return data.publicUrl;
};

export const createLead = async (leadData: Lead): Promise<Lead> => {
  const client = ensureSupabaseClient();
  
  console.log('Creating lead with data:', leadData);
  console.log('Supabase client configured for URL:', client.supabaseUrl);
  
  const { data, error } = await client
    .from('leads')
    .insert([leadData])
    .select()
    .single();

  if (error) {
    console.error('Supabase error details:', error);
    console.error('Error code:', error.code);
    console.error('Error hint:', error.hint);
    console.error('Error details:', error.details);
    throw new Error(`Failed to create lead: ${error.message} (Code: ${error.code})`);
  }

  console.log('Lead created successfully:', data);
  return data;
};

export const syncToHighLevel = async (leadId: string): Promise<void> => {
  const client = ensureSupabaseClient();
  
  const { error } = await client.functions.invoke('sync-to-highlevel', {
    body: { leadId }
  });

  if (error) {
    throw new Error(`Failed to sync to HighLevel: ${error.message}`);
  }
};