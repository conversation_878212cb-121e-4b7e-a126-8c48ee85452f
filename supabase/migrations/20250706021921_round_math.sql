/*
  # Fix leads table RLS policy for anonymous inserts

  1. Security Updates
    - Drop existing conflicting policies
    - Create new policy to allow anonymous users to insert leads
    - Ensure authenticated users can still read and update leads
*/

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Allow public insert for form submissions" ON leads;
DROP POLICY IF EXISTS "Allow authenticated users to read all leads" ON leads;
DROP POLICY IF EXISTS "Allow authenticated users to update leads" ON leads;

-- Create new policy for anonymous inserts (form submissions)
CREATE POLICY "Enable insert for anonymous users" ON leads
  FOR INSERT TO anon
  WITH CHECK (true);

-- Create policy for authenticated users to read all leads
CREATE POLICY "Enable read for authenticated users" ON leads
  FOR SELECT TO authenticated
  USING (true);

-- Create policy for authenticated users to update leads
CREATE POLICY "Enable update for authenticated users" ON leads
  FOR UPDATE TO authenticated
  USING (true)
  WITH CHECK (true);