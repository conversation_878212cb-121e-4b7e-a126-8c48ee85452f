/*
  # Fix RLS Policies for Leads Table - Final Solution
  
  This migration completely resets and fixes the RLS policies for the leads table
  to resolve the "new row violates row-level security policy" error.
  
  ## Problem
  Anonymous users are getting RLS policy violations when trying to insert leads
  through the form submission.
  
  ## Solution
  1. Drop ALL existing policies to start clean
  2. Create simple, working policies with correct permissions
  3. Ensure anonymous users can INSERT leads
  4. Ensure authenticated users can SELECT, UPDATE, and INSERT leads
  5. Fix storage policies for image uploads
  
  ## Security Model
  - Anonymous users: INSERT only (form submissions)
  - Authenticated users: SELECT, INSERT, UPDATE (admin access)
  - No DELETE permissions for anonymous users
*/

-- First, let's check current state and clean up completely
DO $$
BEGIN
    RAISE NOTICE 'Starting RLS policy cleanup and fix...';
END $$;

-- Disable RLS temporarily to ensure we can clean up
ALTER TABLE public.leads DISABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies on leads table (comprehensive cleanup)
DROP POLICY IF EXISTS "Allow public insert for form submissions" ON public.leads;
DROP POLICY IF EXISTS "Allow authenticated users to read all leads" ON public.leads;
DROP POLICY IF EXISTS "Allow authenticated users to update leads" ON public.leads;
DROP POLICY IF EXISTS "Enable insert for anonymous users" ON public.leads;
DROP POLICY IF EXISTS "Allow anonymous lead creation" ON public.leads;
DROP POLICY IF EXISTS "Allow authenticated lead creation" ON public.leads;
DROP POLICY IF EXISTS "anon_insert_leads" ON public.leads;
DROP POLICY IF EXISTS "authenticated_insert_leads" ON public.leads;
DROP POLICY IF EXISTS "authenticated_select_leads" ON public.leads;
DROP POLICY IF EXISTS "authenticated_update_leads" ON public.leads;
DROP POLICY IF EXISTS "Enable insert for anon users" ON public.leads;
DROP POLICY IF EXISTS "Allow anon insert" ON public.leads;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON public.leads;
DROP POLICY IF EXISTS "Enable read for authenticated users" ON public.leads;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON public.leads;
DROP POLICY IF EXISTS "Allow authenticated read" ON public.leads;
DROP POLICY IF EXISTS "Allow authenticated update" ON public.leads;

-- Re-enable RLS
ALTER TABLE public.leads ENABLE ROW LEVEL SECURITY;

-- Create new, clean policies with descriptive names
-- Policy 1: Allow anonymous users to insert leads (for form submissions)
CREATE POLICY "anon_can_insert_leads"
  ON public.leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- Policy 2: Allow authenticated users to insert leads (for admin)
CREATE POLICY "authenticated_can_insert_leads"
  ON public.leads
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Policy 3: Allow authenticated users to select all leads (for admin)
CREATE POLICY "authenticated_can_select_leads"
  ON public.leads
  FOR SELECT
  TO authenticated
  USING (true);

-- Policy 4: Allow authenticated users to update leads (for admin)
CREATE POLICY "authenticated_can_update_leads"
  ON public.leads
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Verify RLS is enabled
DO $$
BEGIN
    IF NOT (SELECT rowsecurity FROM pg_tables WHERE tablename = 'leads' AND schemaname = 'public') THEN
        RAISE EXCEPTION 'RLS is not enabled on leads table!';
    END IF;
    RAISE NOTICE 'RLS is properly enabled on leads table';
END $$;

-- Clean up storage policies and recreate them
-- Drop existing storage policies
DROP POLICY IF EXISTS "Allow public upload to project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read from project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous upload to project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous read from project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated upload to project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated delete from project-images" ON storage.objects;

-- Ensure storage bucket exists and is public
INSERT INTO storage.buckets (id, name, public)
VALUES ('project-images', 'project-images', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Create storage policies for anonymous users (needed for form submissions)
CREATE POLICY "anon_can_upload_project_images"
  ON storage.objects
  FOR INSERT
  TO anon
  WITH CHECK (bucket_id = 'project-images');

-- Create storage policy for public read access
CREATE POLICY "public_can_read_project_images"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'project-images');

-- Create storage policies for authenticated users (admin access)
CREATE POLICY "authenticated_can_upload_project_images"
  ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'project-images');

CREATE POLICY "authenticated_can_delete_project_images"
  ON storage.objects
  FOR DELETE
  TO authenticated
  USING (bucket_id = 'project-images');

-- Final verification
DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    -- Count policies on leads table
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE tablename = 'leads' AND schemaname = 'public';
    
    IF policy_count != 4 THEN
        RAISE EXCEPTION 'Expected 4 policies on leads table, found %', policy_count;
    END IF;
    
    RAISE NOTICE 'Successfully created % policies on leads table', policy_count;
    
    -- Count storage policies
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE tablename = 'objects' AND schemaname = 'storage';
    
    RAISE NOTICE 'Created % storage policies', policy_count;
    
    RAISE NOTICE 'RLS policy fix completed successfully!';
END $$;
