/*
  # Fix leads table RLS policy for anonymous inserts

  1. Security Updates
    - Drop existing INSERT policy that may be too restrictive
    - Create new policy that properly allows anonymous users to insert leads
    - Ensure the policy allows all required fields to be inserted

  2. Changes
    - Remove any conflicting INSERT policies
    - Add comprehensive INSERT policy for anonymous users
    - Maintain security while allowing form submissions
*/

-- Drop existing INSERT policies that might be conflicting
DROP POLICY IF EXISTS "Allow anonymous inserts" ON leads;
DROP POLICY IF EXISTS "Enable insert for anon users" ON leads;
DROP POLICY IF EXISTS "Allow anon insert" ON leads;

-- Create a comprehensive INSERT policy for anonymous users
CREATE POLICY "Allow anonymous lead creation"
  ON leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- Also ensure authenticated users can insert (for admin purposes)
CREATE POLICY "Allow authenticated lead creation"
  ON leads
  FOR INSERT
  TO authenticated
  WITH CHECK (true);