/*
  # Fix leads table RLS policy for anonymous users

  1. Security Updates
    - Drop existing INSERT policy for anonymous users
    - Create new INSERT policy with proper permissions for anonymous users
    - Ensure anonymous users can insert leads without authentication

  2. Changes
    - Remove old policy that may have conflicting conditions
    - Add clear policy allowing anonymous inserts to leads table
*/

-- Drop the existing policy if it exists
DROP POLICY IF EXISTS "Enable insert for anonymous users" ON leads;

-- Create a new policy that explicitly allows anonymous users to insert leads
CREATE POLICY "Allow anonymous lead creation"
  ON leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- Ensure the policy is properly applied
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;