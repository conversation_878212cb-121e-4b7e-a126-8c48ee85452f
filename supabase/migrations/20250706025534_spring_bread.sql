/*
  # Fix RLS policies for anonymous form submissions

  1. Security Updates
    - Drop conflicting policies
    - Create proper policy for anonymous inserts
    - Ensure storage bucket policies work for anonymous users
    - Add proper policies for authenticated users

  2. Changes
    - Allow anonymous users to insert leads (form submissions)
    - Allow authenticated users to read and update leads
    - Fix storage policies for image uploads
*/

-- Temporarily disable <PERSON><PERSON> to clean up policies
ALTER TABLE leads DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies to avoid conflicts
DROP POLICY IF EXISTS "Allow anonymous inserts" ON leads;
DROP POLICY IF EXISTS "Allow authenticated read" ON leads;
DROP POLICY IF EXISTS "Allow authenticated update" ON leads;
DROP POLICY IF EXISTS "Allow anonymous lead creation" ON leads;
DROP POLICY IF EXISTS "Enable insert for anonymous users" ON leads;
DROP POLICY IF EXISTS "Allow public insert for form submissions" ON leads;
DROP POLICY IF EXISTS "Enable read for authenticated users" ON leads;
DROP POLICY IF EXISTS "Allow authenticated users to read all leads" ON leads;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON leads;
DROP POLICY IF EXISTS "Allow authenticated users to update leads" ON leads;

-- Re-enable RLS
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;

-- Create new policies with clear names and proper permissions
CREATE POLICY "Allow anonymous inserts"
  ON leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

CREATE POLICY "Allow authenticated read"
  ON leads
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated update"
  ON leads
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Ensure the storage bucket exists and is public
INSERT INTO storage.buckets (id, name, public)
VALUES ('project-images', 'project-images', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Clean up storage policies
DROP POLICY IF EXISTS "Allow anonymous upload to project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read from project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated upload to project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated delete from project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public upload to project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous read from project-images" ON storage.objects;

-- Create storage policies for anonymous users (needed for form submissions)
CREATE POLICY "Allow anonymous upload to project-images"
  ON storage.objects
  FOR INSERT
  TO anon
  WITH CHECK (bucket_id = 'project-images');

CREATE POLICY "Allow public read from project-images"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'project-images');

-- Allow authenticated users to manage storage
CREATE POLICY "Allow authenticated upload to project-images"
  ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'project-images');

CREATE POLICY "Allow authenticated delete from project-images"
  ON storage.objects
  FOR DELETE
  TO authenticated
  USING (bucket_id = 'project-images');