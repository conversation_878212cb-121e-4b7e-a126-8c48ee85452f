# Horizon Carpentry & Handyman - Lead Generation Form

A modern, responsive lead generation form with image upload capabilities that integrates with Supabase and HighLevel CRM.

## Features

- ✅ Beautiful, responsive form design
- ✅ Image upload with compression
- ✅ Google Places address autocomplete
- ✅ Supabase database integration
- ✅ HighLevel CRM synchronization
- ✅ Real-time form validation
- ✅ Mobile-optimized interface

## Tech Stack

- **Frontend**: React + TypeScript + Tailwind CSS
- **Backend**: Supabase (Database + Storage + Edge Functions)
- **CRM Integration**: HighLevel API
- **Deployment**: Netlify (recommended)

## Setup Instructions

### 1. Supabase Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Run the migration to create the database schema:
   - Go to SQL Editor in Supabase Dashboard
   - Copy and paste the contents of `supabase/migrations/create_leads_table.sql`
   - Execute the migration
3. Deploy the edge function:
   - The edge function code is in `supabase/functions/sync-to-highlevel/index.ts`
   - This will be deployed automatically when you connect to Supabase

### 2. HighLevel Setup

1. Get your HighLevel API credentials:
   - API Key from HighLevel settings
   - Location ID from your HighLevel account
2. Create custom fields in HighLevel (optional):
   - `project_image_url` - for storing image URLs
   - `project_image_filename` - for storing original filenames

### 3. Environment Variables

Create a `.env` file with the following variables:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
HIGHLEVEL_API_KEY=your_highlevel_api_key
HIGHLEVEL_LOCATION_ID=your_highlevel_location_id
```

### 4. Google Maps API (Optional)

1. Enable Google Places API in Google Cloud Console
2. Create an API key and add it to your environment variables
3. This enables address autocomplete functionality

## How It Works

1. **Form Submission**: User fills out form and optionally uploads an image
2. **Image Processing**: Images are compressed and uploaded to Supabase Storage
3. **Data Storage**: Form data is saved to Supabase database
4. **CRM Sync**: Edge function automatically syncs data to HighLevel
5. **Image Integration**: Project images are uploaded to HighLevel media library

## Deployment

### Recommended: Netlify

1. Connect your repository to Netlify
2. Set environment variables in Netlify dashboard
3. Deploy automatically on git push

### Alternative: Vercel

1. Connect repository to Vercel
2. Set environment variables
3. Deploy

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## Form Data Flow

```
User Form → Supabase Storage (images) → Supabase Database → Edge Function → HighLevel CRM
```

## Benefits of This Architecture

1. **Reliability**: Supabase provides enterprise-grade infrastructure
2. **Scalability**: Handles high traffic and large file uploads
3. **Data Integrity**: All submissions are stored even if HighLevel sync fails
4. **Performance**: Images are served from CDN
5. **Flexibility**: Easy to add new integrations or modify data flow

## Support

For questions or issues, please check the documentation or create an issue in the repository.