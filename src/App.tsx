import React from 'react';
import { SupabaseSetup } from './components/SupabaseSetup';
import Navigation from './Navigation';
import HeroSection from './HeroSection';

// Check if Supabase is configured
const isSupabaseConfigured = () => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  return supabaseUrl && 
         supabaseAnonKey && 
         supabaseUrl !== 'your_supabase_project_url' && 
         supabaseAnonKey !== 'your_supabase_anon_key' &&
         supabaseUrl.startsWith('https://');
};

function App() {
  // Show setup screen if Supabase is not configured
  if (!isSupabaseConfigured()) {
    return <SupabaseSetup />;
  }

  return (
    <div className="min-h-screen">
      <Navigation />
      <HeroSection />
    </div>
  );
}

export default App;
