/*
  # Verify and Fix RLS Policies for Leads Table

  1. Check Current Policies
    - List all existing policies on leads table
    - Verify RLS is enabled
  
  2. Fix Policies
    - Drop any conflicting policies
    - Create simple, working policies for anonymous and authenticated users
    
  3. Test Access
    - Verify anonymous users can insert leads
*/

-- First, check if <PERSON><PERSON> is enabled (should be true)
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'leads' AND schemaname = 'public';

-- Check current policies
SELECT 
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'leads' AND schemaname = 'public';

-- Drop ALL existing INSERT policies to start fresh
DROP POLICY IF EXISTS "Allow anonymous lead creation" ON public.leads;
DROP POLICY IF EXISTS "Allow authenticated lead creation" ON public.leads;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON public.leads;
DROP POLICY IF EXISTS "Enable insert for anon users" ON public.leads;
DROP POLICY IF EXISTS "Allow anon insert" ON public.leads;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.leads;

-- Create simple, working policies
CREATE POLICY "anon_insert_leads"
  ON public.leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

CREATE POLICY "authenticated_insert_leads"
  ON public.leads
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Verify the new policies
SELECT 
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'leads' AND schemaname = 'public';