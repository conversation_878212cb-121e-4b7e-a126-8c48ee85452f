/*
  # Fix RLS policy for anonymous lead creation

  1. Security Changes
    - Drop existing conflicting policies
    - Create a clear policy for anonymous inserts
    - Ensure RLS is properly enabled
    - Add policy for public read access to storage

  2. Storage Policies
    - Ensure anonymous users can upload images
    - Ensure public read access to uploaded images
*/

-- First, disable <PERSON><PERSON> temporarily to clean up
ALTER TABLE leads DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Allow anonymous lead creation" ON leads;
DROP POLICY IF EXISTS "Enable insert for anonymous users" ON leads;
DROP POLICY IF EXISTS "Allow public insert for form submissions" ON leads;
DROP POLICY IF EXISTS "Enable read for authenticated users" ON leads;
DROP POLICY IF EXISTS "Allow authenticated users to read all leads" ON leads;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON leads;
DROP POLICY IF EXISTS "Allow authenticated users to update leads" ON leads;

-- Re-enable RLS
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;

-- Create simple, clear policies
CREATE POLICY "Allow anonymous inserts"
  ON leads
  FOR INSERT
  TO anon
  WITH CHECK (true);

CREATE POLICY "Allow authenticated read"
  ON leads
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated update"
  ON leads
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Ensure storage bucket exists and is public
INSERT INTO storage.buckets (id, name, public)
VALUES ('project-images', 'project-images', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Drop existing storage policies to avoid conflicts
DROP POLICY IF EXISTS "Allow public upload to project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read from project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous upload to project-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous read from project-images" ON storage.objects;

-- Create storage policies for anonymous users
CREATE POLICY "Allow anonymous upload to project-images"
  ON storage.objects
  FOR INSERT
  TO anon
  WITH CHECK (bucket_id = 'project-images');

CREATE POLICY "Allow public read from project-images"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'project-images');

-- Also allow authenticated users to manage storage
CREATE POLICY "Allow authenticated upload to project-images"
  ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'project-images');

CREATE POLICY "Allow authenticated delete from project-images"
  ON storage.objects
  FOR DELETE
  TO authenticated
  USING (bucket_id = 'project-images');